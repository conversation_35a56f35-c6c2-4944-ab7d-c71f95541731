<?xml version="1.0" encoding="UTF-8"?>
<svg width="1200" height="960" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="grad-header" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#1e3a8a;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#3b82f6;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="grad-interface" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#059669;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#10b981;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="grad-agent" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#991b1b;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#dc2626;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="grad-tech" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#7c3aed;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#a855f7;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="grad-data" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#ea580c;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#f97316;stop-opacity:1" />
    </linearGradient>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#4b5563" />
    </marker>
  </defs>
  
  <!-- Background -->
  <rect width="1200" height="960" fill="#f8fafc"/>
  
  <!-- Title -->
  <rect x="0" y="0" width="1200" height="60" fill="url(#grad-header)"/>
  <text x="600" y="38" text-anchor="middle" fill="white" font-size="24" font-weight="bold" font-family="sans-serif">CloudBot 智能体分层系统架构</text>
  
  <!-- Layer 1: Interface & Integration Layer -->
  <g id="layer-interface">
    <rect x="50" y="80" width="1100" height="130" fill="url(#grad-interface)" rx="10" opacity="0.95"/>
    <text x="70" y="110" fill="white" font-size="18" font-weight="bold" font-family="sans-serif">接口与集成层 (Interface &amp; Integration Layer)</text>
    
    <rect x="80" y="135" width="250" height="65" fill="white" rx="5"/>
    <text x="165" y="155" text-anchor="middle" fill="#059669" font-size="14" font-weight="bold" font-family="sans-serif">钉钉 (DingOps) &amp; CIPU</text>
    <text x="165" y="175" text-anchor="middle" fill="#374151" font-size="12" font-family="sans-serif">@CloudBot, 交互诊断</text>
    
    <rect x="350" y="135" width="250" height="65" fill="white" rx="5"/>
    <text x="475" y="155" text-anchor="middle" fill="#059669" font-size="14" font-weight="bold" font-family="sans-serif">Aone 平台</text>
    <text x="475" y="175" text-anchor="middle" fill="#374151" font-size="12" font-family="sans-serif">工单应答, 报告推送</text>

    <rect x="620" y="135" width="250" height="65" fill="white" rx="5"/>
    <text x="745" y="155" text-anchor="middle" fill="#059669" font-size="14" font-weight="bold" font-family="sans-serif">AES (工单客户端)</text>
    <text x="745" y="175" text-anchor="middle" fill="#374151" font-size="12" font-family="sans-serif">智能应答增强</text>

    <rect x="890" y="135" width="250" height="65" fill="white" rx="5"/>
    <text x="1015" y="155" text-anchor="middle" fill="#059669" font-size="14" font-weight="bold" font-family="sans-serif">CloudBot 独立平台</text>
    <text x="1015" y="175" text-anchor="middle" fill="#374151" font-size="12" font-family="sans-serif">高级诊断界面</text>
  </g>
  
  <!-- Layer 2: Agent Architecture Layer -->
  <g id="layer-agent">
    <rect x="50" y="230" width="1100" height="150" fill="url(#grad-agent)" rx="10" opacity="0.95"/>
    <text x="70" y="260" fill="white" font-size="18" font-weight="bold" font-family="sans-serif">Agent 架构层 (Agent Architecture Layer)</text>
    
    <rect x="80" y="285" width="250" height="80" fill="white" rx="5"/>
    <text x="205" y="305" text-anchor="middle" fill="#991b1b" font-size="14" font-weight="bold" font-family="sans-serif">调度路由智能体</text>
    <text x="205" y="325" text-anchor="middle" fill="#374151" font-size="12" font-family="sans-serif">(Dispatcher Agent)</text>
    <text x="205" y="345" text-anchor="middle" fill="#374151" font-size="11" font-family="sans-serif">意图识别, 任务分发</text>

    <rect x="350" y="285" width="250" height="80" fill="white" rx="5"/>
    <text x="475" y="305" text-anchor="middle" fill="#991b1b" font-size="14" font-weight="bold" font-family="sans-serif">交互诊断智能体</text>
    <text x="475" y="325" text-anchor="middle" fill="#374151" font-size="12" font-family="sans-serif">(Interactive Agent)</text>
    <text x="475" y="345" text-anchor="middle" fill="#374151" font-size="11" font-family="sans-serif">RAG, 多轮对话</text>

    <rect x="620" y="285" width="250" height="80" fill="white" rx="5"/>
    <text x="745" y="305" text-anchor="middle" fill="#991b1b" font-size="14" font-weight="bold" font-family="sans-serif">推理诊断智能体</text>
    <text x="745" y="325" text-anchor="middle" fill="#374151" font-size="12" font-family="sans-serif">(Reasoning Agent)</text>
    <text x="745" y="345" text-anchor="middle" fill="#374151" font-size="11" font-family="sans-serif">SOP/自主规划, 多工具调用</text>

    <rect x="890" y="285" width="250" height="80" fill="white" rx="5"/>
    <text x="1015" y="305" text-anchor="middle" fill="#991b1b" font-size="14" font-weight="bold" font-family="sans-serif">测试智能体</text>
    <text x="1015" y="325" text-anchor="middle" fill="#374151" font-size="12" font-family="sans-serif">(Test Agent)</text>
    <text x="1015" y="345" text-anchor="middle" fill="#374151" font-size="11" font-family="sans-serif">用例生成, 测试编排</text>
  </g>

  <!-- Layer 3: Technical Foundation Layer -->
  <g id="layer-tech">
    <rect x="50" y="400" width="1100" height="150" fill="url(#grad-tech)" rx="10" opacity="0.95"/>
    <text x="70" y="430" fill="white" font-size="18" font-weight="bold" font-family="sans-serif">技术底座层 (Technical Foundation Layer)</text>
    
    <rect x="80" y="455" width="250" height="80" fill="white" rx="5"/>
    <text x="205" y="475" text-anchor="middle" fill="#7c3aed" font-size="14" font-weight="bold" font-family="sans-serif">高阶智能体核心</text>
    <text x="205" y="495" text-anchor="middle" fill="#374151" font-size="12" font-family="sans-serif">(Advanced Agent Core)</text>
    <text x="205" y="515" text-anchor="middle" fill="#374151" font-size="11" font-family="sans-serif">微调, 记忆, 迭代规划</text>

    <rect x="350" y="455" width="250" height="80" fill="white" rx="5"/>
    <text x="475" y="475" text-anchor="middle" fill="#7c3aed" font-size="14" font-weight="bold" font-family="sans-serif">知识与上下文管理</text>
    <text x="475" y="495" text-anchor="middle" fill="#374151" font-size="12" font-family="sans-serif">(Knowledge &amp; Context)</text>
    <text x="475" y="515" text-anchor="middle" fill="#374151" font-size="11" font-family="sans-serif">上下文工程, SOP体系</text>

    <rect x="620" y="455" width="250" height="80" fill="white" rx="5"/>
    <text x="745" y="475" text-anchor="middle" fill="#7c3aed" font-size="14" font-weight="bold" font-family="sans-serif">复杂场景推理</text>
    <text x="745" y="495" text-anchor="middle" fill="#374151" font-size="12" font-family="sans-serif">(Complex Reasoning)</text>
    <text x="745" y="515" text-anchor="middle" fill="#374151" font-size="11" font-family="sans-serif">长链条评估, No-SOP诊断</text>

    <rect x="890" y="455" width="250" height="80" fill="white" rx="5"/>
    <text x="1015" y="475" text-anchor="middle" fill="#7c3aed" font-size="14" font-weight="bold" font-family="sans-serif">未来技术潜能</text>
    <text x="1015" y="495" text-anchor="middle" fill="#374151" font-size="12" font-family="sans-serif">(Future Potential)</text>
    <text x="1015" y="515" text-anchor="middle" fill="#374151" font-size="11" font-family="sans-serif">Code Agent, 沙盒环境</text>
  </g>

  <!-- Layer 4: Data & Collaboration Ecosystem -->
  <g id="layer-data">
    <rect x="50" y="570" width="1100" height="130" fill="url(#grad-data)" rx="10" opacity="0.95"/>
    <text x="70" y="600" fill="white" font-size="18" font-weight="bold" font-family="sans-serif">数据与协作生态 (Data &amp; Collaboration Ecosystem)</text>
    
    <rect x="80" y="625" width="250" height="65" fill="white" rx="5"/>
    <text x="205" y="645" text-anchor="middle" fill="#ea580c" font-size="14" font-weight="bold" font-family="sans-serif">ECS-MCP 工具</text>
    <text x="205" y="665" text-anchor="middle" fill="#374151" font-size="12" font-family="sans-serif">能力复用与协同</text>
    
    <rect x="350" y="625" width="250" height="65" fill="white" rx="5"/>
    <text x="475" y="645" text-anchor="middle" fill="#ea580c" font-size="14" font-weight="bold" font-family="sans-serif">统一知识库</text>
    <text x="475" y="665" text-anchor="middle" fill="#374151" font-size="12" font-family="sans-serif">知识体系共建</text>

    <rect x="620" y="625" width="250" height="65" fill="white" rx="5"/>
    <text x="745" y="645" text-anchor="middle" fill="#ea580c" font-size="14" font-weight="bold" font-family="sans-serif">ChatBI 数据分析</text>
    <text x="745" y="665" text-anchor="middle" fill="#374151" font-size="12" font-family="sans-serif">数据智能融合</text>

    <rect x="890" y="625" width="250" height="65" fill="white" rx="5"/>
    <text x="1015" y="645" text-anchor="middle" fill="#ea580c" font-size="14" font-weight="bold" font-family="sans-serif">历史工单数据</text>
    <text x="1015" y="665" text-anchor="middle" fill="#374151" font-size="12" font-family="sans-serif">SOP 提炼来源</text>
  </g>

  <!-- Layer 5: Infrastructure Layer -->
  <g id="layer-infra">
    <rect x="50" y="720" width="1100" height="100" fill="#374151" rx="10" opacity="0.95"/>
    <text x="70" y="750" fill="white" font-size="18" font-weight="bold" font-family="sans-serif">基础设施层 (Infrastructure Layer)</text>
    <text x="600" y="785" text-anchor="middle" fill="#d1d5db" font-size="14" font-family="sans-serif">云计算平台 | 容器化部署 (K8s) | 微服务架构 | 监控告警 | 安全防护</text>
  </g>
  
  <!-- Arrows -->
  <line x1="600" y1="210" x2="600" y2="230" stroke="#4b5563" stroke-width="2" marker-end="url(#arrowhead)"/>
  <line x1="600" y1="380" x2="600" y2="400" stroke="#4b5563" stroke-width="2" marker-end="url(#arrowhead)"/>
  <line x1="600" y1="550" x2="600" y2="570" stroke="#4b5563" stroke-width="2" marker-end="url(#arrowhead)"/>
  <line x1="600" y1="700" x2="600" y2="720" stroke="#4b5563" stroke-width="2" marker-end="url(#arrowhead)"/>
  <line x1="600" y1="820" x2="600" y2="840" stroke="#4b5563" stroke-width="2" marker-end="url(#arrowhead)"/>

</svg>
